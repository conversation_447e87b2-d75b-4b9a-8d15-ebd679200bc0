<template>
    <div class="bg">
        <div class="w1280">
            <div class="flex">
                <back>返回</back>
                <div class="title">回复我的</div>
            </div>
            <div class="boxItem" v-for="(item,index) in boxList" :key="index">
                <div class="topBox">
                    <div class="userAndtime">
                        <div class="userName">{{ item.userName }}</div>
                        <div class="desc">回复</div>
                        <div class="time">{{ item.time }}</div>
                    </div>
                    <div class="rightBtn">
                        <div class="btn" @click="openReplyDialog(item)">
                            <img src="@/assets/pinglun_def.png" alt="" class="btnImg">
                            <div class="number">{{ item.plnm }}</div>
                        </div>
                        <div class="btn">
                            <img src="@/assets/delete.png" alt="" class="btnImg">
                        </div>
                    </div>
                </div>
                <div class="content">{{ item.content }}</div>
                <div class="imgBox">
                    <div class="imgList" v-for="(imgItem,imgIndex) in item.imgList" :key="imgIndex">
                        <img :src="imgItem.img" alt="" class="imgSize">
                    </div>
                </div>
                <div class="replyBox" v-if="item.replyList.length > 0">
                    <div class="replyItem" v-for="(reply, replyIndex) in item.replyList" :key="replyIndex">
                        <div class="userAndtime">
                            <div class="userName">{{ reply.userName }}：</div>
                            <div class="replyContent">{{ reply.content }}</div>
                        </div>
                        <div class="imgBox" v-if="reply.imgList.length > 0">
                            <div class="imgList" v-for="(imgItem,imgIndex) in reply.imgList" :key="imgIndex">
                                <img :src="imgItem.img" alt="" class="imgSize">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div v-if="boxList.length == 0" class="zantuneirong">
                <img src="@/assets/zanwu.png" alt="">
                <div class="text">暂无回复~</div>
            </div>
        </div>
        <el-pagination
            v-model:current-page="pageParams.pageNum"
            v-model:page-size="pageParams.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :background="true"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            style="margin-top: 20px; justify-content: center;"
        />
        <ReplyDialog
            v-model:visible="replyDialogVisible"
            :parentId="currentReplyId"
            :replyName="currentReplyName"
            @refresh="loadData"
        />

    </div>
</template>
    
<script setup>
import { useRoute } from 'vue-router';
import { getMessageBoardReplyList } from '@/api/study.js'
import ReplyDialog from './ReplyDialog.vue'
const route = useRoute()
const replyDialogVisible = ref(false)
const currentReplyId = ref('')
const currentReplyName = ref('')
const total = ref(0)
const boxList = ref([]);
const pageParams = ref({
    pageNum: 1,
    pageSize: 10,
    requestType:3,
    researchOfficeId: route.query.id
})

const handleSizeChange = (val) => {
    pageParams.value.pageSize = val
    pageParams.value.pageNum = 1
    loadData()
}

const handleCurrentChange = (val) => {
    pageParams.value.pageNum = val
    loadData()
};

const openReplyDialog = (item) => {
    currentReplyId.value = item.id
    currentReplyName.value = item.userName
    replyDialogVisible.value = true
}
const loadData = () => {
    getMessageBoardReplyList(pageParams.value).then(res => {
        boxList.value = res.data
        total.value = res.page.total
    })
}

onMounted(() => {
    loadData()
});

</script>
      
<style lang="scss" scoped>
.bg {
  background: #f5f7fa;
  padding: 31px;
}
.box{
    display: flex;
    margin-top: 20px;
}
.leftBox{
    margin-right: 20px;
}
.rightBox{
    width: 236px;
    height: 300px;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    padding: 30px;
}
.boxItem{
    min-height: 149px;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    box-sizing: border-box;
    padding: 30px;
    margin-bottom: 20px;
}
.topBox{
    display: flex;
    justify-content: space-between;
}
.userAndtime{
    display: flex;
    align-items: center;
}
.userName{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 14px;
    color: #43474D;
    margin-right: 16px;
}
.time{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 13px;
    color: #878D99;
    margin-top: 4px;
}
.rightBtn{
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.btn{
    display: flex;
    align-items: center;
    margin-right: 60px;
    cursor: pointer;
}
.btn:last-child{
    margin-right: 0px;
}
.btnImg{
    width: 24px;
    height: 24px;
    margin-right: 8px;
}
.number{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #878D99;
}
.content{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #44474D;
    line-height: 28px;
    margin-top: 12px;
}
.imgBox{
    display: grid;
    gap: 16px;
    grid-template-columns: repeat(6, 1fr);
    margin-top: 16px;
}
.imgSize{
    width: 180px;
    height: 135px;
    border-radius: 8px 8px 8px 8px;
}
.imgSize:last-child{
    margin-right: 0;
}
.flex{
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}
.title{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 20px;
    color: #2D2F33;
    margin-left: 32px;
}
.replyNumber{
    color:#2D2F33;
}
.replyContent{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #44474D;
    line-height: 28px;
}


.replyBox{
    margin-top: 16px;
}
.replyItem{
    padding: 20px;
    min-height: 94px;
    background: #F5F7FA;
    border-radius: 8px 8px 8px 8px;
    margin-bottom: 12px;
}
.replyItem:last-child{
    margin-bottom: 0;
}
.desc{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 13px;
    color: #878D99;
    margin-right: 16px;
}
.zantuneirong{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    height: 590px;
    background-color: #fff;
}
.text{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #878D99;
    margin-top: 20px;
}
</style>
  